package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trinasolar.integration.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @className: InteAppSystemChange
 * @Description: 应用系统变更记录
 * @author: pengshy
 * @date: 2025/9/2 14:31
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tasp_base.inte_app_system_change")
public class InteAppSystemChange implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 应用系统ID
     */
    private String systemId;

    /**
     * 应用系统编码
     */
    @TableField(value = "simple_en_name", jdbcType = JdbcType.VARCHAR, typeHandler = org.apache.ibatis.type.StringTypeHandler.class)
    private String simpleEnName;

    /**
     * 变更类型(1:新增,2:更新,3:删除)
     */
    private Integer changeType;

    /**
     * 变更后版本号 （数字自增）
     */
    private Integer currentVersion;

    /**
     * 变更后数据(JSON)
     */
    private String currentData;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;

    /**
     * 是否已处理(0:否,1:是)
     */
    private Integer isProcessed;


    /**
     * 默认记录创建时间字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 默认记录创建人ID字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 默认记录创建人姓名字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 默认记录修改时间字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 默认记录修改人ID字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updaterId;

    /**
     * 默认记录修改人姓名字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updaterName;

    /**
     * 默认逻辑删除标记，is_deleted=0有效，新建时由数据库赋值0
     */
    @TableLogic
    @JsonIgnore
    @TableField(value = "is_deleted", select = false)
    private Integer deleted;


    @TableField(value = "tenant_id")
    private Integer tenantId;
}
