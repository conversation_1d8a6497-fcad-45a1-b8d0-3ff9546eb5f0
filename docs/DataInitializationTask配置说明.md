# 数据初始化任务配置说明

## 功能概述

`DataInitializationTask` 是一个在项目启动后自动执行的数据初始化任务，主要功能是：

1. 检查应用系统变更表和应用程序变更表的初始化状态
2. 全量对比源表和变更表的数据
3. 将变更表中缺失的数据进行补充初始化

## 核心特性

### 1. 启动时自动执行
- 实现了 `ApplicationRunner` 接口
- 在 Spring Boot 应用完全启动后自动执行

### 2. 分布式锁保护
- 使用 Redis 分布式锁确保集群环境下只有一个实例执行初始化
- 锁名称：`tasp:data:initialization:lock`
- 锁等待时间：10秒
- 锁自动释放时间：60秒

### 3. 全量数据对比
- 不再简单检查版本为0的记录数量
- 全量对比源表和变更表数据
- 只初始化变更表中缺失的记录

### 4. 容错处理
- 单条记录插入失败不影响其他记录
- 详细的日志记录便于问题排查

## 工作流程

```mermaid
graph TD
    A[应用启动] --> B[获取分布式锁]
    B --> C{获取锁成功?}
    C -->|否| D[跳过初始化]
    C -->|是| E[查询应用系统全量数据]
    E --> F[查询应用系统变更表版本0记录]
    F --> G[对比找出缺失数据]
    G --> H[插入缺失的应用系统变更记录]
    H --> I[查询应用程序全量数据]
    I --> J[查询应用程序变更表版本0记录]
    J --> K[对比找出缺失数据]
    K --> L[插入缺失的应用程序变更记录]
    L --> M[释放锁]
    M --> N[初始化完成]
```

## 配置要求

### 1. Redis 配置
确保项目中已配置 Redisson 客户端：

```yaml
# application.yml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

### 2. 数据库配置
确保以下 Mapper 已正确配置：
- `InteAppSystemChangeMapper`
- `ApplicationProgramChangeMapper`
- `AppSystemMapper`
- `ApplicationProgramMapper`

## 日志输出示例

### 正常初始化流程
```
2025-09-03 10:00:00.000 INFO  - 开始执行数据初始化任务...
2025-09-03 10:00:00.001 INFO  - 获取到初始化锁，开始执行数据初始化...
2025-09-03 10:00:00.002 INFO  - 开始检查应用系统变更表初始化状态...
2025-09-03 10:00:00.010 INFO  - 查询到应用系统数据 50 条，开始全量对比变更表...
2025-09-03 10:00:00.015 INFO  - 变更表中已存在版本为0的记录 30 条
2025-09-03 10:00:00.050 INFO  - 应用系统变更表初始化完成，跳过 30 条已存在记录，成功插入 20 条新记录
2025-09-03 10:00:00.051 INFO  - 开始检查应用程序变更表初始化状态...
2025-09-03 10:00:00.060 INFO  - 查询到应用程序数据 100 条，开始全量对比变更表...
2025-09-03 10:00:00.065 INFO  - 变更表中已存在版本为0的记录 80 条
2025-09-03 10:00:00.100 INFO  - 应用程序变更表初始化完成，跳过 80 条已存在记录，成功插入 20 条新记录
2025-09-03 10:00:00.101 INFO  - 数据初始化任务执行完成
2025-09-03 10:00:00.102 INFO  - 释放初始化锁
```

### 无需初始化场景
```
2025-09-03 10:00:00.000 INFO  - 开始执行数据初始化任务...
2025-09-03 10:00:00.001 INFO  - 获取到初始化锁，开始执行数据初始化...
2025-09-03 10:00:00.002 INFO  - 开始检查应用系统变更表初始化状态...
2025-09-03 10:00:00.010 INFO  - 查询到应用系统数据 50 条，开始全量对比变更表...
2025-09-03 10:00:00.015 INFO  - 变更表中已存在版本为0的记录 50 条
2025-09-03 10:00:00.020 INFO  - 应用系统变更表初始化完成，跳过 50 条已存在记录，成功插入 0 条新记录
```

## 监控建议

### 1. 关键指标监控
- 初始化任务执行时间
- 插入记录数量
- 失败记录数量

### 2. 告警配置
- 初始化任务执行失败告警
- 初始化时间过长告警（超过5分钟）
- 大量记录插入失败告警

## 注意事项

1. **首次部署**：首次部署时会进行全量初始化，可能耗时较长
2. **数据一致性**：确保源表和变更表的字段映射正确
3. **性能影响**：大量数据初始化时可能对数据库造成压力，建议在业务低峰期部署
4. **锁超时**：如果初始化时间超过60秒，锁会自动释放，可能导致重复执行

## 故障排查

### 1. 初始化失败
检查日志中的错误信息，常见问题：
- 数据库连接问题
- 字段映射错误
- JSON序列化失败

### 2. 重复初始化
检查分布式锁配置是否正确，确保Redis连接正常

### 3. 数据不一致
检查源表和变更表的数据映射逻辑是否正确
