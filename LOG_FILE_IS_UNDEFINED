2025-09-04 08:44:25.632 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:44:25.709 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:44:25.711 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:25.711 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:25.711 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:25.711 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:44:25.711 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:44:25.712 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:25.712 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:25.768 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:44:25.773 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:44:25.774 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:44:26.301 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 08:44:26.342 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 08:44:26.374 | [31m WARN 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 08:44:26.393 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 08:44:26.419 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 08:44:26.420 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 08:44:27.696 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 08:44:27.699 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 08:44:27.767 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2025-09-04 08:44:28.041 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=33324d0a-f995-3cc9-b857-b14a8953aa71
2025-09-04 08:44:28.128 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:44:28.140 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:44:28.141 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:44:28.142 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:28.142 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:44:28.150 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:44:28.158 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:44:28.158 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:44:28.321 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:44:28.323 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:44:28.324 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$658/0x00000008005a1c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:44:28.327 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:44:28.781 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 08:44:28.792 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 08:44:28.792 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 08:44:28.997 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 08:44:28.997 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2558 ms
2025-09-04 08:44:29.301 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 08:44:29.373 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 08:44:29.875 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 08:44:29.876 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 08:44:29.877 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 08:44:29.941 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 08:44:32.260 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 08:44:32.270 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 08:44:32.270 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 08:44:32.270 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 08:44:32.271 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 08:44:32.271 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 08:44:32.271 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 08:44:32.272 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 08:44:32.273 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 08:44:32.570 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 08:44:32.660 | [31m WARN 54972[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 08:44:32.990 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756946671000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756946672000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756946673573 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756946673000 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756946674576 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756946674000 3 connected

2025-09-04 08:44:33.040 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 08:44:33.040 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 08:44:33.056 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-27[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 08:44:33.346 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 08:44:33.352 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-25[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 08:44:33.373 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-30[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 08:44:33.384 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 08:44:33.384 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-12[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 08:44:33.399 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 08:44:33.705 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 08:44:33.706 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 08:44:33.707 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 08:44:33.711 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 08:44:33.712 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 08:44:33.713 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 08:44:33.719 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 08:44:33.719 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 08:44:33.719 | [34m INFO 54972[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 08:44:34.837 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 08:44:35.347 | [31m WARN 54972[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 08:44:35.504 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:44:35.505 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 08:44:35.506 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:44:35.762 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:44:35.771 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:44:35.794 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 08:44:35.799 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 08:44:35.800 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 08:44:35.808 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 08:44:35.824 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 11.34 seconds (JVM running for 12.655)
2025-09-04 08:44:35.826 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 08:44:35.877 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 08:44:35.877 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 08:44:36.050 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963402781082976257
2025-09-04 08:44:36.112 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 08:44:36.113 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 08:44:36.237 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 08:44:36.404 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 08:44:36.404 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 08:44:36.404 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 08:44:36.534 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 425 条，开始全量对比变更表...
2025-09-04 08:44:36.698 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 08:44:36.772 | [1;31mERROR 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 插入应用程序变更记录失败，appId: 251, programCode: null, programNameEn: a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789a123456789

org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'app_code' at row 1
### The error may exist in com/trinasolar/integration/api/mapper/ApplicationProgramChangeMapper.java (best guess)
### The error may involve com.trinasolar.integration.api.mapper.ApplicationProgramChangeMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tasp_develop.application_program_change  ( id, app_id, app_code, system_id, change_type, current_version, current_data, operator, change_time, is_processed,  create_time, update_time, create_by, update_by )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,  ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'app_code' at row 1
; Data truncation: Data too long for column 'app_code' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'app_code' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy118.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy175.insert(Unknown Source)
	at com.trinasolar.integration.task.DataInitializationTask.initializeApplicationProgramChangeTable(DataInitializationTask.java:303)
	at com.trinasolar.integration.task.DataInitializationTask.run(DataInitializationTask.java:128)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:485)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:497)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.trinasolar.integration.KeplerIntegrationServerApplication.main(KeplerIntegrationServerApplication.java:20)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'app_code' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy199.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy198.update(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy198.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 23 common frames omitted

2025-09-04 08:44:36.773 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 08:44:36.773 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 08:44:36.787 | [34m INFO 54972[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:44:36.788 | [34m INFO 54972[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:44:36.798 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 08:44:36.814 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 08:44:36.817 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:44:36.817 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 08:44:36.817 | [34m INFO 54972[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:46:27.616 | [31m WARN 54972[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 08:46:27.616 | [31m WARN 54972[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 08:46:27.616 | [31m WARN 54972[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 08:46:27.617 | [31m WARN 54972[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 08:46:27.650 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 08:46:27.651 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 08:46:27.651 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 08:46:27.659 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 08:46:27.659 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 08:46:30.665 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 08:46:30.666 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 08:46:33.673 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 08:46:36.687 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 08:46:36.687 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 08:46:36.687 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 08:46:36.688 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 08:46:36.688 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 08:46:36.688 | [31m WARN 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 08:46:36.688 | [31m WARN 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 08:46:36.688 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 08:46:36.688 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 08:46:36.688 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 08:46:36.688 | [31m WARN 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 08:46:36.725 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 08:46:36.727 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 08:46:36.734 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 08:46:36.734 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 08:46:36.734 | [34m INFO 54972[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 08:46:41.894 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:46:41.976 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:46:41.978 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:41.978 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:41.978 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:41.979 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:46:41.979 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:46:41.979 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:41.979 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:42.067 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:46:42.073 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:46:42.074 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:46:42.727 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 08:46:42.767 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 08:46:42.833 | [31m WARN 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 08:46:42.851 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 08:46:42.896 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 08:46:42.897 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 08:46:44.762 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 08:46:44.768 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 08:46:44.917 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 105 ms. Found 0 Redis repository interfaces.
2025-09-04 08:46:45.588 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=33324d0a-f995-3cc9-b857-b14a8953aa71
2025-09-04 08:46:45.725 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:46:45.751 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:46:45.751 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:46:45.751 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:46:45.751 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:46:45.752 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:46:45.752 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:46:45.752 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:45.752 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:45.752 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:46:45.752 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:46:45.752 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:45.753 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:46:45.771 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:46:45.791 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:46:45.791 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:46:46.057 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:46:46.059 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:46:46.060 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$658/0x00000008005a1c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:46:46.063 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:46:47.021 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 08:46:47.042 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 08:46:47.043 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 08:46:47.318 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 08:46:47.319 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 4368 ms
2025-09-04 08:46:47.822 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 08:46:47.989 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 08:46:48.884 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 08:46:48.885 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 08:46:48.886 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 08:46:49.003 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 08:46:54.086 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 08:46:54.100 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 08:46:54.100 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 08:46:54.101 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 08:46:54.101 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 08:46:54.101 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 08:46:54.101 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 08:46:54.102 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 08:46:54.103 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 08:46:54.564 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 08:46:54.664 | [31m WARN 55414[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 08:46:54.997 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756946816000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756946815000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756946815000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756946816149 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756946817152 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756946815145 3 connected

2025-09-04 08:46:55.055 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 08:46:55.055 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 08:46:55.078 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 08:46:55.356 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 08:46:55.367 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-27[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 08:46:55.396 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-9[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 08:46:55.396 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-10[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 08:46:55.402 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-15[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 08:46:55.424 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-2[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 08:46:55.690 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 08:46:55.690 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 08:46:55.690 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 08:46:55.690 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 08:46:55.690 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 08:46:55.690 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 08:46:55.724 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 08:46:55.725 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 08:46:55.725 | [34m INFO 55414[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 08:46:57.624 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 08:46:58.229 | [31m WARN 55414[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 08:46:58.515 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:46:58.517 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 08:46:58.517 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:46:58.800 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:46:58.808 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:46:58.834 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 08:46:58.840 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 08:46:58.841 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 08:46:58.850 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 08:46:58.878 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 18.648 seconds (JVM running for 19.397)
2025-09-04 08:46:58.881 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 08:46:58.923 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 08:46:58.923 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 08:46:59.047 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963403381069783042
2025-09-04 08:46:59.085 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 08:46:59.085 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 08:46:59.199 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 08:46:59.241 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 08:46:59.241 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 08:46:59.241 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 08:46:59.290 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 424 条，开始全量对比变更表...
2025-09-04 08:46:59.364 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 08:46:59.365 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 08:46:59.365 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 08:46:59.390 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 08:46:59.407 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 08:46:59.408 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:46:59.409 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 08:46:59.409 | [34m INFO 55414[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:46:59.830 | [34m INFO 55414[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:46:59.831 | [34m INFO 55414[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:47:32.423 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 08:47:32.423 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 08:47:32.425 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 2 ms
2025-09-04 08:47:32.521 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 08:47:32.628 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(106 ms)]
2025-09-04 08:47:55.448 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 08:47:55.459 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(10 ms)]
2025-09-04 08:47:58.170 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756946883799})]
2025-09-04 08:47:58.482 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(311 ms)]
2025-09-04 08:50:02.571 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756947008324})]
2025-09-04 08:50:02.652 | [34m INFO 55414[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(80 ms)]
2025-09-04 08:50:17.299 | [31m WARN 55414[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 08:50:17.299 | [31m WARN 55414[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 08:50:17.300 | [31m WARN 55414[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 08:50:17.301 | [31m WARN 55414[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 08:50:17.329 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 08:50:17.329 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 08:50:17.329 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 08:50:17.338 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 08:50:17.338 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 08:50:19.607 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 08:50:19.607 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 08:50:20.245 | [34m INFO 55414[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:50:20.247 | [34m INFO 55414[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:50:20.248 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 08:50:23.254 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 08:50:23.255 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 08:50:23.255 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 08:50:23.255 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 08:50:23.255 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 08:50:23.256 | [31m WARN 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 08:50:23.256 | [31m WARN 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 08:50:23.256 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 08:50:23.256 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 08:50:23.257 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 08:50:23.257 | [31m WARN 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 08:50:23.313 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 08:50:23.317 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 08:50:23.325 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 08:50:23.325 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 08:50:23.325 | [34m INFO 55414[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 08:50:26.546 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:50:26.618 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:50:26.620 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:26.620 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:26.620 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:26.620 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:50:26.620 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:50:26.620 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:26.620 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:26.679 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:50:26.683 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:50:26.684 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:50:27.135 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 08:50:27.178 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 08:50:27.220 | [31m WARN 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 08:50:27.242 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 08:50:27.271 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 08:50:27.272 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 08:50:28.589 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 08:50:28.592 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 08:50:28.648 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-09-04 08:50:28.929 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=33324d0a-f995-3cc9-b857-b14a8953aa71
2025-09-04 08:50:29.002 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:50:29.021 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:29.022 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:50:29.023 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:50:29.023 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:29.023 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:50:29.031 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:50:29.039 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:50:29.039 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:50:29.251 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:50:29.254 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:50:29.256 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$658/0x00000008005a1c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:50:29.258 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:50:29.985 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 08:50:29.997 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 08:50:29.998 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 08:50:30.287 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 08:50:30.287 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2993 ms
2025-09-04 08:50:30.630 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 08:50:30.692 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 08:50:31.121 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 08:50:31.121 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 08:50:31.121 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 08:50:31.177 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 08:50:33.496 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 08:50:33.505 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 08:50:33.505 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 08:50:33.505 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 08:50:33.505 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 08:50:33.505 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 08:50:33.505 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 08:50:33.507 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 08:50:33.507 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 08:50:33.775 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 08:50:33.857 | [31m WARN 56275[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 08:50:34.158 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756947036000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756947033000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756947035033 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756947036036 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756947034030 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756947034000 3 connected

2025-09-04 08:50:34.206 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 08:50:34.209 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 08:50:34.226 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 08:50:34.569 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 08:50:34.598 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-4[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 08:50:34.598 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-28[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 08:50:34.614 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-10[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 08:50:34.645 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 08:50:34.645 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 08:50:35.008 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 08:50:35.009 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 08:50:35.010 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 08:50:35.020 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 08:50:35.020 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 08:50:35.020 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 08:50:35.021 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 08:50:35.022 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 08:50:35.022 | [34m INFO 56275[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 08:50:36.373 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 08:50:36.977 | [31m WARN 56275[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 08:50:37.213 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:50:37.215 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 08:50:37.215 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:50:37.468 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:50:37.477 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:50:37.494 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 08:50:37.497 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 08:50:37.498 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 08:50:37.507 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 08:50:37.528 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 12.189 seconds (JVM running for 13.182)
2025-09-04 08:50:37.530 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 08:50:37.573 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 08:50:37.573 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 08:50:37.689 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963404298150125570
2025-09-04 08:50:37.726 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 08:50:37.727 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 08:50:37.811 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 08:50:37.849 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 08:50:37.849 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 08:50:37.849 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 08:50:37.884 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 424 条，开始全量对比变更表...
2025-09-04 08:50:37.981 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 08:50:37.982 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 08:50:37.982 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 08:50:38.009 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 08:50:38.023 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 08:50:38.024 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:50:38.024 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 08:50:38.024 | [34m INFO 56275[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:50:38.493 | [34m INFO 56275[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:50:38.494 | [34m INFO 56275[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:51:32.957 | [31m WARN 56275[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 08:51:32.957 | [31m WARN 56275[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 08:51:32.957 | [31m WARN 56275[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 08:51:32.958 | [31m WARN 56275[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 08:51:32.985 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 08:51:32.986 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 08:51:32.986 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 08:51:33.084 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 08:51:33.084 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 08:51:36.093 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 08:51:36.094 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 08:51:38.565 | [34m INFO 56275[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:51:38.568 | [34m INFO 56275[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:51:38.569 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 08:51:41.580 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 08:51:41.580 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 08:51:41.580 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 08:51:41.581 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 08:51:41.581 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 08:51:41.581 | [31m WARN 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 08:51:41.581 | [31m WARN 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 08:51:41.581 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 08:51:41.581 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 08:51:41.581 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 08:51:41.581 | [31m WARN 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 08:51:41.617 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 08:51:41.619 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 08:51:41.626 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 08:51:41.626 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 08:51:41.626 | [34m INFO 56275[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 08:51:46.087 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:51:46.138 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:51:46.139 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:46.139 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:46.140 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:46.140 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:51:46.140 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:51:46.140 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:46.140 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:46.182 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:51:46.185 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:51:46.186 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:51:46.547 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 08:51:46.571 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 08:51:46.595 | [31m WARN 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 08:51:46.611 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 08:51:46.637 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 08:51:46.638 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 08:51:47.533 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 08:51:47.535 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 08:51:47.590 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-09-04 08:51:47.844 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=648d2612-16b7-3186-9d70-88a38195a382
2025-09-04 08:51:47.890 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:51:47.903 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:51:47.903 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:51:47.903 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:47.904 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:51:47.912 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:51:47.920 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:51:47.921 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:51:48.062 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:51:48.064 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:51:48.065 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a7840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:51:48.066 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:51:48.456 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 08:51:48.463 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 08:51:48.464 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 08:51:48.578 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 08:51:48.578 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 1921 ms
2025-09-04 08:51:48.874 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 08:51:48.932 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 08:51:49.325 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 08:51:49.326 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 08:51:49.326 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 08:51:49.374 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 08:51:51.346 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 08:51:51.350 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 08:51:51.351 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 08:51:51.351 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 08:51:51.351 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 08:51:51.351 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 08:51:51.351 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 08:51:51.352 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 08:51:51.353 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 08:51:51.580 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 08:51:51.655 | [31m WARN 56546[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 08:51:51.888 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756947109000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756947110000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756947113000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756947111334 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756947113343 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756947112000 3 connected

2025-09-04 08:51:51.941 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 08:51:51.941 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 08:51:51.954 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 08:51:52.305 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 08:51:52.311 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 08:51:52.331 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 08:51:52.350 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 08:51:52.350 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-14[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 08:51:52.376 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 08:51:52.702 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 08:51:52.702 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 08:51:52.702 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 08:51:52.703 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 08:51:52.703 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 08:51:52.703 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 08:51:52.703 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 08:51:52.703 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 08:51:52.703 | [34m INFO 56546[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 08:51:53.643 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 08:51:53.987 | [31m WARN 56546[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 08:51:54.121 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:51:54.122 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 08:51:54.122 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:51:54.324 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:51:54.330 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:51:54.345 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 08:51:54.349 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 08:51:54.350 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 08:51:54.369 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 08:51:54.387 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 9.239 seconds (JVM running for 9.951)
2025-09-04 08:51:54.388 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 08:51:54.426 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 08:51:54.426 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 08:51:54.507 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963404620406919170
2025-09-04 08:51:54.538 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 08:51:54.539 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 08:51:54.626 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 08:51:54.663 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 08:51:54.663 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 08:51:54.663 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 08:51:54.694 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 424 条，开始全量对比变更表...
2025-09-04 08:51:54.736 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 08:51:54.737 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 08:51:54.737 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 08:51:54.755 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 08:51:54.766 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 08:51:54.767 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:51:54.768 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 08:51:54.768 | [34m INFO 56546[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:51:55.347 | [34m INFO 56546[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:51:55.348 | [34m INFO 56546[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:52:00.017 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:52:00.325 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:53:00.011 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:53:00.122 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:53:00.290 | [1;31mERROR 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$Unauthorized: [401 Unauthorized] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 9923,
  "message" : "未登录！",
  "data" : null,
  "success" : false
}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215)
	at feign.FeignException.errorStatus(FeignException.java:194)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareIncrement(DataShareServiceImpl.java:89)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareIncrement(DataShareSchedule.java:40)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:53:04.423 | [34m INFO 56546[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 08:53:04.423 | [34m INFO 56546[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 08:53:04.425 | [34m INFO 56546[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 2 ms
2025-09-04 08:53:04.515 | [34m INFO 56546[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 08:53:04.615 | [34m INFO 56546[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(98 ms)]
2025-09-04 08:54:00.095 | [1;31mERROR 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$Unauthorized: [401 Unauthorized] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 9923,
  "message" : "未登录！",
  "data" : null,
  "success" : false
}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215)
	at feign.FeignException.errorStatus(FeignException.java:194)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareIncrement(DataShareServiceImpl.java:89)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareProgramIncrement(DataShareSchedule.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:54:00.101 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:54:00.207 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:55:00.075 | [1;31mERROR 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$Unauthorized: [401 Unauthorized] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 9923,
  "message" : "未登录！",
  "data" : null,
  "success" : false
}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215)
	at feign.FeignException.errorStatus(FeignException.java:194)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareFull(DataShareServiceImpl.java:122)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareFull(DataShareSchedule.java:84)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:55:00.080 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:55:00.180 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:56:00.085 | [1;31mERROR 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$InternalServerError: [500 Internal Server Error] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 1,
  "message" : "FAILED",
  "data" : "网络异常",
  "success" : false
}]
	at feign.FeignException.serverErrorStatus(FeignException.java:250)
	at feign.FeignException.errorStatus(FeignException.java:197)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareFull(DataShareServiceImpl.java:122)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareProgramFull(DataShareSchedule.java:107)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:56:00.092 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:56:00.565 | [34m INFO 56546[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:56:12.909 | [31m WARN 56546[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 08:56:12.909 | [31m WARN 56546[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 08:56:12.909 | [31m WARN 56546[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 08:56:12.910 | [31m WARN 56546[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 08:56:12.940 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 08:56:12.941 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 08:56:12.941 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 08:56:12.950 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 08:56:12.950 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 08:56:15.088 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 08:56:15.088 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 08:56:15.700 | [34m INFO 56546[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:56:15.704 | [34m INFO 56546[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 08:56:15.705 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 08:56:18.713 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 08:56:18.713 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 08:56:18.714 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 08:56:18.714 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 08:56:18.714 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 08:56:18.714 | [31m WARN 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 08:56:18.714 | [31m WARN 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 08:56:18.714 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 08:56:18.714 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 08:56:18.714 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 08:56:18.715 | [31m WARN 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 08:56:18.788 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 08:56:18.791 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 08:56:18.801 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 08:56:18.802 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 08:56:18.802 | [34m INFO 56546[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 08:56:24.371 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:56:24.439 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:56:24.440 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:24.440 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:24.441 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:24.441 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:56:24.441 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:56:24.441 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:24.441 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:24.500 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:56:24.505 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:56:24.506 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:56:25.018 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 08:56:25.045 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 08:56:25.068 | [31m WARN 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 08:56:25.081 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 08:56:25.105 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 08:56:25.106 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 08:56:27.334 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 08:56:27.337 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 08:56:27.397 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 42 ms. Found 0 Redis repository interfaces.
2025-09-04 08:56:27.700 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=648d2612-16b7-3186-9d70-88a38195a382
2025-09-04 08:56:27.763 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 08:56:27.778 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:56:27.778 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:56:27.778 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 08:56:27.778 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 08:56:27.778 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:56:27.779 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:56:27.779 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:27.779 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:27.779 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:56:27.779 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 08:56:27.779 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:27.779 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 08:56:27.787 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 08:56:27.796 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 08:56:27.796 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 08:56:27.971 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:56:27.975 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:56:27.977 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a2c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:56:27.979 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 08:56:28.520 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 08:56:28.530 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 08:56:28.530 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 08:56:28.687 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 08:56:28.687 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 3562 ms
2025-09-04 08:56:29.032 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 08:56:29.105 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 08:56:29.535 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 08:56:29.537 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 08:56:29.537 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 08:56:29.593 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 08:56:31.999 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 08:56:32.006 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 08:56:32.006 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 08:56:32.006 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 08:56:32.006 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 08:56:32.006 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 08:56:32.006 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 08:56:32.007 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 08:56:32.007 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 08:56:32.261 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 08:56:32.351 | [31m WARN 57438[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 08:56:32.618 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756947394000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756947392477 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756947391000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756947394485 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756947393000 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756947393481 3 connected

2025-09-04 08:56:32.665 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 08:56:32.665 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 08:56:32.674 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 08:56:32.980 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-25[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 08:56:32.986 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-1[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 08:56:33.002 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 08:56:33.014 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-12[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 08:56:33.024 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 08:56:33.030 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 08:56:33.358 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 08:56:33.360 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 08:56:33.360 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 08:56:33.365 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 08:56:33.366 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 08:56:33.366 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 08:56:33.370 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 08:56:33.371 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 08:56:33.371 | [34m INFO 57438[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 08:56:34.356 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 08:56:34.772 | [31m WARN 57438[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 08:56:34.922 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:56:34.922 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 08:56:34.923 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 08:56:35.124 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:56:35.129 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:56:35.142 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 08:56:35.146 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 08:56:35.147 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 08:56:35.160 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 08:56:35.177 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 11.917 seconds (JVM running for 12.761)
2025-09-04 08:56:35.179 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 08:56:35.213 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 08:56:35.214 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 08:56:35.301 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963405798112960514
2025-09-04 08:56:35.348 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 08:56:35.348 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 08:56:35.474 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 08:56:35.527 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 08:56:35.528 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 08:56:35.528 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 08:56:35.566 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 424 条，开始全量对比变更表...
2025-09-04 08:56:35.659 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 08:56:35.660 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 08:56:35.660 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 08:56:35.693 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 08:56:35.706 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 08:56:35.707 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:56:35.708 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 08:56:35.709 | [34m INFO 57438[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 08:56:36.146 | [34m INFO 57438[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:56:36.147 | [34m INFO 57438[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 08:57:00.011 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:57:00.274 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:57:00.286 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用系统增量共享任务
2025-09-04 08:57:00.463 | [1;31mERROR 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$InternalServerError: [500 Internal Server Error] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 1,
  "message" : "FAILED",
  "data" : "网络异常",
  "success" : false
}]
	at feign.FeignException.serverErrorStatus(FeignException.java:250)
	at feign.FeignException.errorStatus(FeignException.java:197)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareIncrement(DataShareServiceImpl.java:89)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareIncrement(DataShareSchedule.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:57:00.781 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 08:57:00.781 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 08:57:00.784 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 2 ms
2025-09-04 08:57:00.867 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 08:57:00.954 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(86 ms)]
2025-09-04 08:57:52.029 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 08:57:52.044 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(15 ms)]
2025-09-04 08:58:00.001 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用程序增量共享任务
2025-09-04 08:58:00.088 | [1;31mERROR 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$InternalServerError: [500 Internal Server Error] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 1,
  "message" : "FAILED",
  "data" : "网络异常",
  "success" : false
}]
	at feign.FeignException.serverErrorStatus(FeignException.java:250)
	at feign.FeignException.errorStatus(FeignException.java:197)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareIncrement(DataShareServiceImpl.java:89)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareProgramIncrement(DataShareSchedule.java:64)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:58:00.094 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:58:00.190 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:58:21.879 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 08:58:21.892 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(13 ms)]
2025-09-04 08:59:00.000 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用系统全量共享任务
2025-09-04 08:59:00.090 | [1;31mERROR 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$InternalServerError: [500 Internal Server Error] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 1,
  "message" : "FAILED",
  "data" : "网络异常",
  "success" : false
}]
	at feign.FeignException.serverErrorStatus(FeignException.java:250)
	at feign.FeignException.errorStatus(FeignException.java:197)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareFull(DataShareServiceImpl.java:122)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareFull(DataShareSchedule.java:87)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:59:00.091 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用程序全量共享任务
2025-09-04 08:59:00.172 | [1;31mERROR 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.FeignException$InternalServerError: [500 Internal Server Error] during [GET] to [https://tasp-dev.trinasolar.com//harmony/kepler/upms/u/dict/parentType/sync_next_system] [RemoteDictProvider#getDictItemList(String)]: [{
  "code" : 1,
  "message" : "FAILED",
  "data" : "网络异常",
  "success" : false
}]
	at feign.FeignException.serverErrorStatus(FeignException.java:250)
	at feign.FeignException.errorStatus(FeignException.java:197)
	at feign.FeignException.errorStatus(FeignException.java:185)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareFull(DataShareServiceImpl.java:122)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareProgramFull(DataShareSchedule.java:111)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

2025-09-04 08:59:00.181 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 08:59:00.294 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 08:59:28.724 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 08:59:28.736 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(11 ms)]
2025-09-04 09:00:00.006 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:00:00.123 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:00:35.151 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/dashboard/api/count) 参数({startTime=, endTime=})]
2025-09-04 09:00:35.376 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/dashboard/api/count) 耗时(224 ms)]
2025-09-04 09:01:00.008 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:01:00.117 | [34m INFO 57438[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:01:08.916 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 09:01:08.928 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(11 ms)]
2025-09-04 09:01:16.608 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 09:01:16.882 | [34m INFO 57438[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(273 ms)]
2025-09-04 09:01:25.620 | [31m WARN 57438[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 09:01:25.620 | [31m WARN 57438[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 09:01:25.620 | [31m WARN 57438[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 09:01:25.620 | [31m WARN 57438[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 09:01:25.653 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 09:01:25.653 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 09:01:25.653 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 09:01:25.660 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 09:01:25.660 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 09:01:26.003 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 09:01:26.004 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 09:01:26.595 | [34m INFO 57438[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:01:26.599 | [34m INFO 57438[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:01:26.600 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 09:01:29.607 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 09:01:29.607 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 09:01:29.608 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 09:01:29.608 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 09:01:29.608 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 09:01:29.608 | [31m WARN 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 09:01:29.608 | [31m WARN 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 09:01:29.608 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 09:01:29.608 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 09:01:29.609 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 09:01:29.609 | [31m WARN 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 09:01:29.644 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 09:01:29.647 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 09:01:29.657 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 09:01:29.657 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 09:01:29.657 | [34m INFO 57438[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 09:01:34.622 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:01:34.687 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:01:34.688 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:34.688 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:34.689 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:34.689 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:34.689 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:34.690 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:34.690 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:34.742 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:01:34.745 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:01:34.746 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:01:35.147 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 09:01:35.177 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 09:01:35.208 | [31m WARN 58478[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 09:01:35.233 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 09:01:35.262 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 09:01:35.263 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 09:01:36.478 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 09:01:36.481 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 09:01:36.548 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 47 ms. Found 0 Redis repository interfaces.
2025-09-04 09:01:36.837 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=648d2612-16b7-3186-9d70-88a38195a382
2025-09-04 09:01:36.896 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:01:36.910 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:36.911 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:36.912 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:36.919 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:01:36.928 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:01:36.928 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:01:37.085 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:37.087 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:37.088 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a2c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:37.090 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:37.538 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 09:01:37.549 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 09:01:37.549 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 09:01:37.674 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 09:01:37.674 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2374 ms
2025-09-04 09:01:37.954 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 09:01:38.014 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 09:01:38.375 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 09:01:38.376 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 09:01:38.376 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 09:01:38.433 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 09:01:38.820 | [31m WARN 58478[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 09:01:38.824 | [31m WARN 58478[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 09:01:40.746 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 09:01:40.754 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 09:01:40.754 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 09:01:40.754 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 09:01:40.754 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 09:01:40.754 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 09:01:40.755 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 09:01:40.755 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 09:01:40.755 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 09:01:40.996 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 09:01:41.073 | [31m WARN 58478[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 09:01:41.374 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756947702000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756947700000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756947701730 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756947700725 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756947702734 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756947701000 3 connected

2025-09-04 09:01:41.450 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 09:01:41.462 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 09:01:41.468 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 09:01:41.910 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 09:01:41.928 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-27[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 09:01:41.941 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 09:01:41.959 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-12[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 09:01:41.959 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 09:01:41.971 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 09:01:42.266 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 09:01:42.267 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 09:01:42.268 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 09:01:42.280 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 09:01:42.280 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 09:01:42.280 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 09:01:42.285 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 09:01:42.286 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 09:01:42.286 | [34m INFO 58478[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 09:01:43.332 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 09:01:43.760 | [31m WARN 58478[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 09:01:43.906 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:01:43.906 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 09:01:43.907 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:01:44.056 | [31m WARN 58478[0;39m | [1;33mmain[0;39m [1;32mConfigServletWebServerApplicationContext[0;39m | Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'nacosWatch'; nested exception is java.lang.RuntimeException: ErrCode:-400, ErrMsg:Shutdown in progress
2025-09-04 09:01:44.059 | [31m WARN 58478[0;39m | [1;33mmain[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 09:01:44.086 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 09:01:44.088 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 09:01:44.094 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 09:01:44.095 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 09:01:44.095 | [34m INFO 58478[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 09:01:47.274 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:01:47.339 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:01:47.341 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:47.341 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:47.341 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:47.341 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:47.341 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:47.342 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:47.342 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:47.390 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:01:47.393 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:01:47.394 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:01:47.785 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 09:01:47.809 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 09:01:47.829 | [31m WARN 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 09:01:47.845 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 09:01:47.870 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 09:01:47.871 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 09:01:49.014 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 09:01:49.017 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 09:01:49.079 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-09-04 09:01:49.393 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=648d2612-16b7-3186-9d70-88a38195a382
2025-09-04 09:01:49.448 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:49.461 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:49.462 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:01:49.462 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:49.462 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:01:49.469 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:01:49.476 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:01:49.476 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:01:49.629 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:49.632 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:49.633 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a2c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:49.635 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:01:50.046 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 09:01:50.055 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 09:01:50.056 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 09:01:50.202 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 09:01:50.202 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2312 ms
2025-09-04 09:01:50.561 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 09:01:50.632 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 09:01:51.028 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 09:01:51.029 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 09:01:51.030 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 09:01:51.084 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 09:01:53.393 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 09:01:53.398 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 09:01:53.398 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 09:01:53.398 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 09:01:53.398 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 09:01:53.398 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 09:01:53.398 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 09:01:53.399 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 09:01:53.399 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 09:01:53.634 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 09:01:53.706 | [31m WARN 58520[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 09:01:53.931 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756947712000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756947713000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756947715786 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756947714783 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756947713780 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756947715000 3 connected

2025-09-04 09:01:53.979 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 09:01:53.986 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 09:01:54.001 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 09:01:54.332 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 09:01:54.349 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-28[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 09:01:54.349 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-4[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 09:01:54.368 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-10[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 09:01:54.378 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-15[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 09:01:54.378 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 09:01:54.720 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 09:01:54.720 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 09:01:54.720 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 09:01:54.721 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 09:01:54.721 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 09:01:54.721 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 09:01:54.722 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 09:01:54.722 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 09:01:54.722 | [34m INFO 58520[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 09:01:55.726 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 09:01:56.147 | [31m WARN 58520[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 09:01:56.299 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:01:56.299 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 09:01:56.299 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:01:56.553 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 09:01:56.558 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 09:01:56.571 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 09:01:56.575 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 09:01:56.575 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 09:01:56.584 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 09:01:56.607 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 10.517 seconds (JVM running for 11.303)
2025-09-04 09:01:56.609 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 09:01:56.656 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 09:01:56.656 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 09:01:57.022 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963407146371952641
2025-09-04 09:01:57.126 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 09:01:57.126 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 09:01:57.211 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 09:01:57.270 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 09:01:57.271 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 09:01:57.271 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 09:01:57.574 | [34m INFO 58520[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 09:01:57.575 | [34m INFO 58520[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 09:01:58.064 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 424 条，开始全量对比变更表...
2025-09-04 09:01:58.181 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 09:01:58.181 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 09:01:58.181 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 09:01:58.200 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 09:01:58.213 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 09:01:58.214 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 09:01:58.214 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 09:01:58.214 | [34m INFO 58520[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 09:02:00.001 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用系统增量共享任务
2025-09-04 09:02:00.270 | [1;31mERROR 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.codec.DecodeException: Error decoding response
	at feign.InvocationContext.proceed(InvocationContext.java:40)
	at feign.AsyncResponseHandler.decode(AsyncResponseHandler.java:116)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:89)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareIncrement(DataShareServiceImpl.java:89)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareIncrement(DataShareSchedule.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
Caused by: java.lang.RuntimeException: Error decoding response
	at com.trinasolar.integration.api.config.ApiConfig.lambda$feignDecoder$0(ApiConfig.java:39)
	at feign.InvocationContext.proceed(InvocationContext.java:36)
	... 22 common frames omitted
Caused by: com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.time.LocalDateTime` from String "2025-08-04 14:33:27": Failed to deserialize java.time.LocalDateTime: (java.time.format.DateTimeParseException) Text '2025-08-04 14:33:27' could not be parsed at index 10
 at [Source: (String)"{
  "code" : 0,
  "message" : "SUCCESS",
  "data" : [ {
    "id" : "1952256550383779841",
    "createdTime" : "2025-08-04 14:33:27",
    "creatorId" : "6",
    "creatorName" : "杨刚 (Yang Gang)",
    "updatedTime" : "2025-08-26 20:55:06",
    "updaterId" : "1923296746045517826",
    "updaterName" : "彭书友",
    "tenantId" : 1,
    "parentId" : "0",
    "dictId" : "1951082751018491906",
    "value" : "devops",
    "label" : "DevOps",
    "type" : "sync_next_system",
    "sort" : null,
    "descriptio"[truncated 2103 chars]; line: 6, column: 21] (through reference chain: com.trinasolar.integration.api.dto.R["data"]->java.util.ArrayList[0]->com.trinasolar.integration.api.entity.DictItem["createdTime"])
	at com.fasterxml.jackson.databind.exc.InvalidFormatException.from(InvalidFormatException.java:67)
	at com.fasterxml.jackson.databind.DeserializationContext.weirdStringException(DeserializationContext.java:1991)
	at com.fasterxml.jackson.databind.DeserializationContext.handleWeirdStringValue(DeserializationContext.java:1219)
	at com.fasterxml.jackson.datatype.jsr310.deser.JSR310DeserializerBase._handleDateTimeException(JSR310DeserializerBase.java:176)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer._fromString(LocalDateTimeDeserializer.java:179)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer.deserialize(LocalDateTimeDeserializer.java:81)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer.deserialize(LocalDateTimeDeserializer.java:40)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:314)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:314)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3629)
	at com.trinasolar.integration.api.config.ApiConfig.lambda$feignDecoder$0(ApiConfig.java:37)
	... 23 common frames omitted
Caused by: java.time.format.DateTimeParseException: Text '2025-08-04 14:33:27' could not be parsed at index 10
	at java.base/java.time.format.DateTimeFormatter.parseResolved0(DateTimeFormatter.java:2046)
	at java.base/java.time.format.DateTimeFormatter.parse(DateTimeFormatter.java:1948)
	at java.base/java.time.LocalDateTime.parse(LocalDateTime.java:492)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer._fromString(LocalDateTimeDeserializer.java:177)
	... 38 common frames omitted

2025-09-04 09:02:00.287 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:02:00.485 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:03:00.002 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用程序增量共享任务
2025-09-04 09:03:00.115 | [1;31mERROR 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mo.s.s.s.TaskUtils$LoggingErrorHandler   [0;39m | Unexpected error occurred in scheduled task

feign.codec.DecodeException: Error decoding response
	at feign.InvocationContext.proceed(InvocationContext.java:40)
	at feign.AsyncResponseHandler.decode(AsyncResponseHandler.java:116)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:89)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy177.getDictItemList(Unknown Source)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.buildTargetSystemTopics(DataShareServiceImpl.java:297)
	at com.trinasolar.integration.service.impl.DataShareServiceImpl.executeDataShareIncrement(DataShareServiceImpl.java:89)
	at com.trinasolar.integration.task.DataShareSchedule.executeDataShareProgramIncrement(DataShareSchedule.java:64)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
Caused by: java.lang.RuntimeException: Error decoding response
	at com.trinasolar.integration.api.config.ApiConfig.lambda$feignDecoder$0(ApiConfig.java:39)
	at feign.InvocationContext.proceed(InvocationContext.java:36)
	... 22 common frames omitted
Caused by: com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.time.LocalDateTime` from String "2025-08-04 14:33:27": Failed to deserialize java.time.LocalDateTime: (java.time.format.DateTimeParseException) Text '2025-08-04 14:33:27' could not be parsed at index 10
 at [Source: (String)"{
  "code" : 0,
  "message" : "SUCCESS",
  "data" : [ {
    "id" : "1952256550383779841",
    "createdTime" : "2025-08-04 14:33:27",
    "creatorId" : "6",
    "creatorName" : "杨刚 (Yang Gang)",
    "updatedTime" : "2025-08-26 20:55:06",
    "updaterId" : "1923296746045517826",
    "updaterName" : "彭书友",
    "tenantId" : 1,
    "parentId" : "0",
    "dictId" : "1951082751018491906",
    "value" : "devops",
    "label" : "DevOps",
    "type" : "sync_next_system",
    "sort" : null,
    "descriptio"[truncated 2103 chars]; line: 6, column: 21] (through reference chain: com.trinasolar.integration.api.dto.R["data"]->java.util.ArrayList[0]->com.trinasolar.integration.api.entity.DictItem["createdTime"])
	at com.fasterxml.jackson.databind.exc.InvalidFormatException.from(InvalidFormatException.java:67)
	at com.fasterxml.jackson.databind.DeserializationContext.weirdStringException(DeserializationContext.java:1991)
	at com.fasterxml.jackson.databind.DeserializationContext.handleWeirdStringValue(DeserializationContext.java:1219)
	at com.fasterxml.jackson.datatype.jsr310.deser.JSR310DeserializerBase._handleDateTimeException(JSR310DeserializerBase.java:176)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer._fromString(LocalDateTimeDeserializer.java:179)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer.deserialize(LocalDateTimeDeserializer.java:81)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer.deserialize(LocalDateTimeDeserializer.java:40)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:314)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:355)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:314)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3629)
	at com.trinasolar.integration.api.config.ApiConfig.lambda$feignDecoder$0(ApiConfig.java:37)
	... 23 common frames omitted
Caused by: java.time.format.DateTimeParseException: Text '2025-08-04 14:33:27' could not be parsed at index 10
	at java.base/java.time.format.DateTimeFormatter.parseResolved0(DateTimeFormatter.java:2046)
	at java.base/java.time.format.DateTimeFormatter.parse(DateTimeFormatter.java:1948)
	at java.base/java.time.LocalDateTime.parse(LocalDateTime.java:492)
	at com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer._fromString(LocalDateTimeDeserializer.java:177)
	... 38 common frames omitted

2025-09-04 09:03:00.122 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:03:00.232 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:04:00.011 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:04:00.113 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:04:32.705 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 09:04:32.706 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 09:04:32.707 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 1 ms
2025-09-04 09:04:32.797 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 09:04:32.900 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(102 ms)]
2025-09-04 09:04:51.465 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/app-market/api/page) 参数({pubOrgName=, tagId=, pageNo=1, apiPath=, name=, pageSize=10})]
2025-09-04 09:04:51.467 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/app-market/api/page) 参数({pubOrgName=, tagId=, pageNo=1, name=, pageSize=10})]
2025-09-04 09:04:51.690 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/app-market/api/page) 耗时(222 ms)]
2025-09-04 09:04:51.690 | [34m INFO 58520[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/app-market/api/page) 耗时(224 ms)]
2025-09-04 09:05:00.008 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:05:00.139 | [34m INFO 58520[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:05:38.746 | [31m WARN 58520[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 09:05:38.746 | [31m WARN 58520[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 09:05:38.746 | [31m WARN 58520[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 09:05:38.747 | [31m WARN 58520[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 09:05:38.791 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 09:05:38.791 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 09:05:38.792 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 09:05:38.799 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 09:05:38.799 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 09:05:41.808 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 09:05:41.809 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 09:05:44.819 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 09:05:47.830 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 09:05:47.830 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 09:05:47.831 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 09:05:47.831 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 09:05:47.831 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 09:05:47.831 | [31m WARN 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 09:05:47.831 | [31m WARN 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 09:05:47.831 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 09:05:47.831 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 09:05:47.831 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 09:05:47.832 | [31m WARN 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 09:05:47.871 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 09:05:47.873 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 09:05:47.880 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 09:05:47.880 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 09:05:47.880 | [34m INFO 58520[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 09:12:09.266 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:12:09.322 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:12:09.323 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:09.323 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:09.323 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:09.324 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:09.324 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:09.324 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:09.324 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:09.370 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:12:09.373 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:12:09.374 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:12:09.806 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 09:12:09.836 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 09:12:09.861 | [31m WARN 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 09:12:09.877 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 09:12:09.902 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 09:12:09.902 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 09:12:11.133 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 09:12:11.136 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 09:12:11.201 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-09-04 09:12:11.481 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=648d2612-16b7-3186-9d70-88a38195a382
2025-09-04 09:12:11.535 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:11.549 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:11.550 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:11.550 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:11.550 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:11.550 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:11.558 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:12:11.565 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:12:11.565 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:12:11.732 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:11.734 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:11.735 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a2c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:11.737 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:12.175 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 09:12:12.187 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 09:12:12.187 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 09:12:12.322 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 09:12:12.322 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2397 ms
2025-09-04 09:12:12.662 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 09:12:12.738 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 09:12:13.188 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 09:12:13.189 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 09:12:13.190 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 09:12:13.255 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 09:12:16.011 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 09:12:16.021 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 09:12:16.022 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 09:12:16.022 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 09:12:16.022 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 09:12:16.022 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 09:12:16.022 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 09:12:16.023 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 09:12:16.024 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 09:12:16.376 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 09:12:16.464 | [31m WARN 60612[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 09:12:16.781 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756948337000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756948338279 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756948335000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756948336270 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756948334000 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756948337276 3 connected

2025-09-04 09:12:16.833 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 09:12:16.833 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 09:12:16.852 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-27[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 09:12:17.120 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 09:12:17.120 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 09:12:17.162 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-8[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 09:12:17.162 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-9[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 09:12:17.169 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 09:12:17.194 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 09:12:17.493 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 09:12:17.494 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 09:12:17.494 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 09:12:17.500 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 09:12:17.500 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 09:12:17.501 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 09:12:17.525 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 09:12:17.526 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 09:12:17.526 | [34m INFO 60612[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 09:12:18.608 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 09:12:19.397 | [31m WARN 60612[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 09:12:19.663 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:12:19.664 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 09:12:19.665 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:12:19.918 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 09:12:19.928 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 09:12:19.954 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 09:12:19.959 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 09:12:19.960 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 09:12:19.969 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 09:12:19.996 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 11.669 seconds (JVM running for 12.381)
2025-09-04 09:12:19.999 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 09:12:20.053 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 09:12:20.054 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 09:12:20.164 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963409761117896706
2025-09-04 09:12:20.198 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 09:12:20.198 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 09:12:20.303 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 09:12:20.350 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 09:12:20.350 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 09:12:20.350 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 09:12:20.395 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 424 条，开始全量对比变更表...
2025-09-04 09:12:20.481 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 09:12:20.482 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 09:12:20.482 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 09:12:20.503 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 09:12:20.516 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 09:12:20.517 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 09:12:20.518 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 09:12:20.518 | [34m INFO 60612[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 09:12:20.941 | [34m INFO 60612[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 09:12:20.942 | [34m INFO 60612[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 09:12:27.558 | [34m INFO 60612[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 09:12:27.558 | [34m INFO 60612[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 09:12:27.560 | [34m INFO 60612[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 2 ms
2025-09-04 09:12:27.658 | [34m INFO 60612[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/logo) 无参数]
2025-09-04 09:12:27.774 | [34m INFO 60612[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/logo) 耗时(115 ms)]
2025-09-04 09:12:40.756 | [31m WARN 60612[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 09:12:40.756 | [31m WARN 60612[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 09:12:40.756 | [31m WARN 60612[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 09:12:40.757 | [31m WARN 60612[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 09:12:40.788 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 09:12:40.788 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 09:12:40.789 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 09:12:40.798 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 09:12:40.798 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 09:12:43.807 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 09:12:43.807 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 09:12:46.816 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 09:12:49.828 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 09:12:49.828 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 09:12:49.828 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 09:12:49.829 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 09:12:49.829 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 09:12:49.829 | [31m WARN 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 09:12:49.829 | [31m WARN 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 09:12:49.829 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 09:12:49.829 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 09:12:49.830 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 09:12:49.830 | [31m WARN 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 09:12:49.870 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 09:12:49.872 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 09:12:49.880 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 09:12:49.880 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 09:12:49.880 | [34m INFO 60612[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 09:12:52.832 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:12:52.895 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:12:52.896 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:52.896 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:52.896 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:52.897 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:52.897 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:52.897 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:52.897 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:52.945 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:12:52.950 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:12:52.951 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:12:53.387 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 09:12:53.414 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 09:12:53.442 | [31m WARN 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 09:12:53.457 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 09:12:53.481 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 09:12:53.481 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 09:12:54.554 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 09:12:54.557 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 09:12:54.616 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
2025-09-04 09:12:54.903 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=648d2612-16b7-3186-9d70-88a38195a382
2025-09-04 09:12:54.957 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:54.970 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 09:12:54.971 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:54.971 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 09:12:54.979 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 09:12:54.986 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 09:12:54.986 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 09:12:55.140 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:55.143 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:55.144 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a2c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:55.146 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 09:12:55.582 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 09:12:55.591 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 09:12:55.592 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 09:12:55.725 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 09:12:55.725 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2225 ms
2025-09-04 09:12:56.040 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 09:12:56.105 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 09:12:56.557 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 09:12:56.559 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 09:12:56.559 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 09:12:56.619 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 09:12:58.734 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 09:12:58.742 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 09:12:58.742 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 09:12:58.742 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 09:12:58.743 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 09:12:58.743 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 09:12:58.743 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 09:12:58.744 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 09:12:58.744 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 09:12:58.994 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 09:12:59.077 | [31m WARN 60746[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 09:12:59.361 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756948379000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756948380000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756948380456 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756948381459 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756948379447 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756948379000 3 connected

2025-09-04 09:12:59.408 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 09:12:59.409 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 09:12:59.425 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 09:12:59.717 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-25[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 09:12:59.728 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-1[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 09:12:59.739 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 09:12:59.758 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-12[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 09:12:59.758 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 09:12:59.769 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 09:13:00.058 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 09:13:00.058 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 09:13:00.059 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 09:13:00.059 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 09:13:00.059 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 09:13:00.059 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 09:13:00.073 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 09:13:00.074 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 09:13:00.074 | [34m INFO 60746[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 09:13:01.122 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 09:13:01.571 | [31m WARN 60746[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 09:13:01.711 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:13:01.712 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 09:13:01.712 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 09:13:01.958 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:13:01.965 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:13:01.980 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 09:13:01.984 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 09:13:01.985 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 09:13:01.992 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 09:13:02.011 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 10.202 seconds (JVM running for 11.0)
2025-09-04 09:13:02.013 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 09:13:02.053 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 09:13:02.053 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始测试单个记录插入...
2025-09-04 09:13:02.148 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录插入成功，ID: 1963409937257701377
2025-09-04 09:13:02.174 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 测试记录删除成功
2025-09-04 09:13:02.174 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 09:13:02.257 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 72 条，开始全量对比变更表...
2025-09-04 09:13:02.294 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 72 条
2025-09-04 09:13:02.295 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 72 条已存在记录，成功插入 0 条新记录
2025-09-04 09:13:02.295 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 09:13:02.348 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 424 条，开始全量对比变更表...
2025-09-04 09:13:02.407 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 609 条
2025-09-04 09:13:02.408 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 424 条已存在记录，成功插入 0 条新记录
2025-09-04 09:13:02.408 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 09:13:02.425 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 09:13:02.439 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 09:13:02.440 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 09:13:02.441 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 09:13:02.441 | [34m INFO 60746[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 09:13:02.987 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:13:02.988 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:13:12.804 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 09:13:12.804 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 09:13:12.806 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 2 ms
2025-09-04 09:13:12.913 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 09:13:13.277 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(364 ms)]
2025-09-04 09:13:15.271 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/logo) 无参数]
2025-09-04 09:13:15.300 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/logo) 耗时(28 ms)]
2025-09-04 09:13:25.155 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 09:13:25.185 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(29 ms)]
2025-09-04 09:14:00.013 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:14:00.167 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:14:00.180 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用程序增量共享任务
2025-09-04 09:14:00.341 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.k.clients.admin.AdminClientConfig   [0;39m | AdminClientConfig values: 
	bootstrap.servers = [tasp-traffic.trinasolar.com:31058, tasp-traffic.trinasolar.com:31059, tasp-traffic.trinasolar.com:31060]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 60000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-09-04 09:14:00.427 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka version: 3.1.2
2025-09-04 09:14:00.427 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka commitId: f8c67dc3ae0a3265
2025-09-04 09:14:00.427 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka startTimeMs: 1756948440425
2025-09-04 09:14:01.186 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已创建缺失的 Kafka topics: [cmdb.application_system.v1]
2025-09-04 09:14:01.187 | [34m INFO 60746[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | App info kafka.admin.client for adminclient-1 unregistered
2025-09-04 09:14:01.191 | [34m INFO 60746[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics scheduler closed
2025-09-04 09:14:01.192 | [34m INFO 60746[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-09-04 09:14:01.192 | [34m INFO 60746[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics reporters closed
2025-09-04 09:14:01.205 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.k.clients.producer.ProducerConfig   [0;39m | ProducerConfig values: 
	acks = -1
	batch.size = 16384
	bootstrap.servers = [tasp-traffic.trinasolar.com:31058, tasp-traffic.trinasolar.com:31059, tasp-traffic.trinasolar.com:31060]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 60000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2025-09-04 09:14:01.217 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.k.clients.producer.KafkaProducer    [0;39m | [Producer clientId=producer-1] Instantiated an idempotent producer.
2025-09-04 09:14:01.232 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka version: 3.1.2
2025-09-04 09:14:01.232 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka commitId: f8c67dc3ae0a3265
2025-09-04 09:14:01.232 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka startTimeMs: 1756948441232
2025-09-04 09:14:01.257 | [34m INFO 60746[0;39m | [1;33mkafka-producer-network-thread | producer-1[0;39m [1;32morg.apache.kafka.clients.Metadata       [0;39m | [Producer clientId=producer-1] Resetting the last seen epoch of partition cmdb.application_system.v1-0 to 0 since the associated topicId changed from null to 4I9b0fK-T5SCsF8Vc6L6yQ
2025-09-04 09:14:01.257 | [34m INFO 60746[0;39m | [1;33mkafka-producer-network-thread | producer-1[0;39m [1;32morg.apache.kafka.clients.Metadata       [0;39m | [Producer clientId=producer-1] Resetting the last seen epoch of partition cmdb.application_system.v1-1 to 0 since the associated topicId changed from null to 4I9b0fK-T5SCsF8Vc6L6yQ
2025-09-04 09:14:01.257 | [34m INFO 60746[0;39m | [1;33mkafka-producer-network-thread | producer-1[0;39m [1;32morg.apache.kafka.clients.Metadata       [0;39m | [Producer clientId=producer-1] Resetting the last seen epoch of partition cmdb.application_system.v1-2 to 0 since the associated topicId changed from null to 4I9b0fK-T5SCsF8Vc6L6yQ
2025-09-04 09:14:01.259 | [34m INFO 60746[0;39m | [1;33mkafka-producer-network-thread | producer-1[0;39m [1;32morg.apache.kafka.clients.Metadata       [0;39m | [Producer clientId=producer-1] Cluster ID: Q5KFy2ApTeKgrnnjIZ6QXQ
2025-09-04 09:14:01.260 | [34m INFO 60746[0;39m | [1;33mkafka-producer-network-thread | producer-1[0;39m [1;32mo.a.k.c.p.internals.TransactionManager  [0;39m | [Producer clientId=producer-1] ProducerId set to 7000 with epoch 0
2025-09-04 09:14:01.357 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=tasp, version=1
2025-09-04 09:14:01.377 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=scp, version=1
2025-09-04 09:14:01.400 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=trinax, version=1
2025-09-04 09:14:01.418 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=trina-buy, version=1
2025-09-04 09:14:01.451 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=csp, version=1
2025-09-04 09:14:01.473 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=scr, version=1
2025-09-04 09:14:01.496 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=scf, version=1
2025-09-04 09:14:01.520 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=mhr, version=1
2025-09-04 09:14:01.551 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=srm, version=1
2025-09-04 09:14:01.586 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=union-welfare, version=1
2025-09-04 09:14:01.603 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=entertain, version=1
2025-09-04 09:14:01.623 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=trust, version=1
2025-09-04 09:14:01.648 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=devops-document , version=1
2025-09-04 09:14:01.675 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=sn-tracking, version=1
2025-09-04 09:14:01.697 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=pms, version=1
2025-09-04 09:14:01.718 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=gtdp, version=1
2025-09-04 09:14:01.746 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=outdoor, version=1
2025-09-04 09:14:01.775 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=trinastorge, version=1
2025-09-04 09:14:01.797 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=carbon-boot, version=1
2025-09-04 09:14:01.819 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=ehsdmp, version=1
2025-09-04 09:14:01.840 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=cfp, version=1
2025-09-04 09:14:01.859 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=oms, version=1
2025-09-04 09:14:01.883 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=myddc, version=1
2025-09-04 09:14:01.908 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=ehs, version=1
2025-09-04 09:14:01.927 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=lyutestapp, version=1
2025-09-04 09:14:01.946 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=devuser, version=1
2025-09-04 09:14:01.968 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=xt609, version=1
2025-09-04 09:14:01.994 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test-sys01, version=1
2025-09-04 09:14:02.013 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test_wgr, version=1
2025-09-04 09:14:02.030 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=t0620, version=1
2025-09-04 09:14:02.058 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=qwe, version=1
2025-09-04 09:14:02.084 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.102 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=domain, version=1
2025-09-04 09:14:02.122 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=version5, version=1
2025-09-04 09:14:02.139 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test-wgr, version=1
2025-09-04 09:14:02.166 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.185 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test729, version=1
2025-09-04 09:14:02.201 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.218 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.240 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.268 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.288 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.312 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.330 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.348 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=bugfix1694, version=1
2025-09-04 09:14:02.369 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test11csp, version=1
2025-09-04 09:14:02.397 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.418 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.438 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.467 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.486 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test2-1, version=1
2025-09-04 09:14:02.504 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.519 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test2-1, version=1
2025-09-04 09:14:02.537 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test-3, version=1
2025-09-04 09:14:02.559 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test2-1, version=1
2025-09-04 09:14:02.579 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.599 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test813-51, version=1
2025-09-04 09:14:02.617 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test813-52, version=1
2025-09-04 09:14:02.637 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.655 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.673 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=ss, version=1
2025-09-04 09:14:02.690 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test, version=1
2025-09-04 09:14:02.707 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=t8141007, version=1
2025-09-04 09:14:02.726 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=t81410052, version=1
2025-09-04 09:14:02.741 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=t8141348, version=1
2025-09-04 09:14:02.760 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=d8141349, version=1
2025-09-04 09:14:02.776 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test182, version=1
2025-09-04 09:14:02.793 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test183, version=1
2025-09-04 09:14:02.815 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=sdfsdfsdf, version=1
2025-09-04 09:14:02.831 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test903, version=1
2025-09-04 09:14:02.852 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test9031, version=1
2025-09-04 09:14:02.875 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 已推送应用系统共享数据，systemCode=test9032, version=1
2025-09-04 09:14:02.875 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | 数据共享任务执行完成
2025-09-04 09:14:33.204 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/page) 参数({"componentName":"","size":15,"page":1})]
2025-09-04 09:14:33.250 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/page) 耗时(45 ms)]
2025-09-04 09:15:00.008 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:15:00.098 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:15:23.189 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:15:23.190 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:15:38.331 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 09:15:38.559 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(227 ms)]
2025-09-04 09:15:45.564 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=1, taskId=20040})]
2025-09-04 09:15:45.767 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(202 ms)]
2025-09-04 09:15:47.989 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 09:15:48.166 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(176 ms)]
2025-09-04 09:15:50.180 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-10[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/page) 参数({"componentName":"","size":15,"page":1})]
2025-09-04 09:15:50.204 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-10[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/page) 耗时(23 ms)]
2025-09-04 09:15:50.390 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/logo) 无参数]
2025-09-04 09:15:50.404 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/logo) 耗时(13 ms)]
2025-09-04 09:16:00.008 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:16:00.105 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:16:03.245 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:16:03.246 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:16:22.377 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 09:16:22.577 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(199 ms)]
2025-09-04 09:17:00.013 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:17:00.194 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:17:05.705 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/page) 参数({"componentName":"","size":15,"page":1})]
2025-09-04 09:17:05.720 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/page) 耗时(15 ms)]
2025-09-04 09:17:37.427 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-4[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/component) 无参数]
2025-09-04 09:17:37.436 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-4[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/component) 耗时(8 ms)]
2025-09-04 09:18:00.006 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:18:00.105 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:18:30.722 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/dashboard/api/count) 参数({startTime=, endTime=})]
2025-09-04 09:18:30.905 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/dashboard/api/count) 耗时(183 ms)]
2025-09-04 09:18:52.700 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-6[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756948737769})]
2025-09-04 09:18:52.796 | [34m INFO 60746[0;39m | [1;33mhttp-nio-80-exec-6[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(95 ms)]
2025-09-04 09:19:00.006 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 09:19:00.106 | [34m INFO 60746[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 09:19:06.776 | [34m INFO 60746[0;39m | [1;33mkafka-producer-network-thread | producer-1[0;39m [1;32morg.apache.kafka.clients.NetworkClient  [0;39m | [Producer clientId=producer-1] Node -2 disconnected.
2025-09-04 09:19:40.029 | [31m WARN 60746[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 09:19:40.029 | [31m WARN 60746[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 09:19:40.029 | [31m WARN 60746[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 09:19:40.031 | [31m WARN 60746[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 09:19:40.055 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 09:19:40.055 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 09:19:40.055 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 09:19:40.063 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 09:19:40.063 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 09:19:43.075 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 09:19:43.075 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 09:19:43.550 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:19:43.552 | [34m INFO 60746[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 09:19:43.554 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 09:19:46.560 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 09:19:46.560 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 09:19:46.560 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 09:19:46.560 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 09:19:46.561 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 09:19:46.561 | [31m WARN 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 09:19:46.561 | [31m WARN 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 09:19:46.561 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 09:19:46.561 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 09:19:46.561 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 09:19:46.562 | [31m WARN 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 09:19:46.601 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.a.k.clients.producer.KafkaProducer    [0;39m | [Producer clientId=producer-1] Closing the Kafka producer with timeoutMillis = 30000 ms.
2025-09-04 09:19:46.603 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics scheduler closed
2025-09-04 09:19:46.603 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-09-04 09:19:46.603 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics reporters closed
2025-09-04 09:19:46.603 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | App info kafka.producer for producer-1 unregistered
2025-09-04 09:19:46.609 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 09:19:46.611 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 09:19:46.618 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 09:19:46.618 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 09:19:46.618 | [34m INFO 60746[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
