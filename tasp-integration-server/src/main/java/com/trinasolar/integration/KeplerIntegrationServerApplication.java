package com.trinasolar.integration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableAsync
@EnableDiscoveryClient
@EnableScheduling
@SpringBootApplication(scanBasePackages = "com.trinasolar.integration")
@EnableFeignClients(basePackages = {"com.trinasolar.integration.api"})
public class KeplerIntegrationServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(KeplerIntegrationServerApplication.class, args);
    }

}
