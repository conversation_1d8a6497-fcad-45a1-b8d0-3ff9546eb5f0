package com.trinasolar.integration.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.integration.api.entity.AppSystem;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.api.entity.ApplicationProgramChange;
import com.trinasolar.integration.api.entity.InteAppSystemChange;
import com.trinasolar.integration.api.mapper.ApplicationProgramChangeMapper;
import com.trinasolar.integration.api.mapper.InteAppSystemChangeMapper;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import com.trinasolar.tasc.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @className: DataInitializationTask
 * @Description: 项目启动后执行数据初始化任务
 * 检查应用系统和应用程序变更表是否初始化，如果版本为0的记录为空，则查询全量数据并插入到变更表中
 * @author: pengshy
 * @date: 2025/9/3 
 */
@Component
@Slf4j
public class DataInitializationTask implements ApplicationRunner {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private InteAppSystemChangeMapper appSystemChangeMapper;

    @Autowired
    private ApplicationProgramChangeMapper appProgramChangeMapper;

    @Autowired
    private AppSystemMapper appSystemMapper;

    @Autowired
    private ApplicationProgramMapper appProgramMapper;

    private static final String SYSTEM_OPERATOR = "admin";
    private static final Integer INITIAL_VERSION = 1;
    private static final Integer CHANGE_TYPE_INSERT = 1;

    /**
     * 安全处理字符串字段，避免数据库类型不匹配问题
     * @param value 原始字符串值
     * @return 处理后的字符串值
     */
    private String safeStringValue(String value) {
        if (value == null) {
            return null;
        }
        String trimmed = value.trim();
        if (trimmed.isEmpty()) {
            return null;
        }
        // 限制字符串长度，避免超出数据库字段长度限制
        if (trimmed.length() > 255) {
            trimmed = trimmed.substring(0, 255);
        }
        return trimmed;
    }

    /**
     * 测试单个记录插入，用于验证修复效果
     */
    private void testSingleInsert() {
        try {
            log.info("开始测试单个记录插入...");

            LocalDateTime now = LocalDateTime.now();
            InteAppSystemChange testChange = InteAppSystemChange.builder()
                    .systemId("test-system-id")
                    .simpleEnName("test-simple-name")
                    .changeType(CHANGE_TYPE_INSERT)
                    .currentVersion(INITIAL_VERSION)
                    .currentData("{\"test\": \"data\"}")
                    .operator(SYSTEM_OPERATOR)
                    .changeTime(now)
                    .isProcessed(0)
                    .build();

            appSystemChangeMapper.insert(testChange);
            log.info("测试记录插入成功，ID: {}", testChange.getId());

            // 删除测试记录
            appSystemChangeMapper.deleteById(testChange.getId());
            log.info("测试记录删除成功");

        } catch (Exception e) {
            log.error("测试单个记录插入失败", e);
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始执行数据初始化任务...");
        
        // 使用分布式锁确保只有一个实例执行初始化
        RLock initLock = redissonClient.getLock("tasp:datashare:initialization:lock");
        
        try {
            // 尝试获取锁（10秒等待，60秒自动释放）
            boolean isLocked = initLock.tryLock(10, 60, TimeUnit.SECONDS);
            if (isLocked) {
                log.info("获取到初始化锁，开始执行数据初始化...");

                // 先测试单个记录插入，验证修复效果
                testSingleInsert();

                // 初始化应用系统变更表
                initializeAppSystemChangeTable();

                // 初始化应用程序变更表
                initializeApplicationProgramChangeTable();

                log.info("数据初始化任务执行完成");
            } else {
                log.info("未能获取到初始化锁，可能其他实例正在执行初始化任务");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("数据初始化任务被中断", e);
        } catch (Exception e) {
            log.error("数据初始化任务执行失败", e);
        } finally {
            // 确保锁释放
            if (initLock.isHeldByCurrentThread()) {
                initLock.unlock();
                log.info("释放初始化锁");
            }
        }
    }

    /**
     * 初始化应用系统变更表
     */
    private void initializeAppSystemChangeTable() {
        log.info("开始检查应用系统变更表初始化状态...");

        // 查询全量应用系统数据
        List<AppSystem> appSystems = appSystemMapper.selectList(new LambdaQueryWrapper<>());

        if (appSystems == null || appSystems.isEmpty()) {
            log.info("应用系统表中无数据，跳过初始化");
            return;
        }

        log.info("查询到应用系统数据 {} 条，开始全量对比变更表...", appSystems.size());

        // 查询已存在的变更记录（版本为0的记录）
        LambdaQueryWrapper<InteAppSystemChange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InteAppSystemChange::getCurrentVersion, INITIAL_VERSION);
        List<InteAppSystemChange> existingChanges = appSystemChangeMapper.selectList(queryWrapper);

        // 构建已存在的systemId集合，用于快速查找
        Set<String> existingSystemIds = existingChanges.stream()
                .map(InteAppSystemChange::getSystemId)
                .collect(Collectors.toSet());

        log.info("变更表中已存在版本为1的记录 {} 条", existingChanges.size());

        LocalDateTime now = LocalDateTime.now();
        int successCount = 0;
        int skipCount = 0;

        // 遍历所有应用系统，检查是否在变更表中存在
        for (AppSystem appSystem : appSystems) {
            String systemId = String.valueOf(appSystem.getId());

            if (existingSystemIds.contains(systemId)) {
                skipCount++;
                log.debug("应用系统 {} 在变更表中已存在，跳过", systemId);
                continue;
            }

            try {
                // 使用安全方法处理 simpleEnName 字段，确保数据类型兼容
                String simpleEnName = safeStringValue(appSystem.getEnSimpleName());

                InteAppSystemChange change = InteAppSystemChange.builder()
                        .systemId(systemId)
                        .simpleEnName(simpleEnName)
                        .changeType(CHANGE_TYPE_INSERT)
                        .currentVersion(INITIAL_VERSION)
                        .currentData(JsonUtils.toJsonString(appSystem))
                        .operator(SYSTEM_OPERATOR)
                        .changeTime(now)
                        .isProcessed(0)
                        .build();

                appSystemChangeMapper.insert(change);
                successCount++;
                log.debug("成功插入应用系统变更记录，systemId: {}, systemCode: {}, simpleEnName: {}",
                         systemId, appSystem.getCode(), simpleEnName);

            } catch (Exception e) {
                // 如果是数据类型相关的错误，尝试不设置 simpleEnName 字段
                if (e.getMessage() != null && e.getMessage().contains("Cannot determine value type")) {
                    try {
                        log.warn("检测到数据类型不匹配，尝试不设置 simpleEnName 字段重新插入，systemId: {}", systemId);
                        InteAppSystemChange fallbackChange = InteAppSystemChange.builder()
                                .systemId(systemId)
                                .simpleEnName(null) // 设置为 null 避免类型冲突
                                .changeType(CHANGE_TYPE_INSERT)
                                .currentVersion(INITIAL_VERSION)
                                .currentData(JsonUtils.toJsonString(appSystem))
                                .operator(SYSTEM_OPERATOR)
                                .changeTime(now)
                                .isProcessed(0)
                                .build();

                        appSystemChangeMapper.insert(fallbackChange);
                        successCount++;
                        log.info("使用 fallback 方式成功插入应用系统变更记录，systemId: {}, systemCode: {}",
                                systemId, appSystem.getCode());
                    } catch (Exception fallbackException) {
                        log.error("Fallback 插入也失败，systemId: {}, systemCode: {}",
                                 systemId, appSystem.getCode(), fallbackException);
                    }
                } else {
                    log.error("插入应用系统变更记录失败，systemId: {}, systemCode: {}, enSimpleName: {}",
                             systemId, appSystem.getCode(), appSystem.getEnSimpleName(), e);
                }
            }
        }

        log.info("应用系统变更表初始化完成，跳过 {} 条已存在记录，成功插入 {} 条新记录",
                skipCount, successCount);
    }

    /**
     * 初始化应用程序变更表
     */
    private void initializeApplicationProgramChangeTable() {
        log.info("开始检查应用程序变更表初始化状态...");

        // 查询全量应用程序数据
        List<ApplicationProgram> applicationPrograms = appProgramMapper.selectList(new LambdaQueryWrapper<>());

        if (applicationPrograms == null || applicationPrograms.isEmpty()) {
            log.info("应用程序表中无数据，跳过初始化");
            return;
        }

        log.info("查询到应用程序数据 {} 条，开始全量对比变更表...", applicationPrograms.size());

        // 查询已存在的变更记录（版本为0的记录）
        LambdaQueryWrapper<ApplicationProgramChange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApplicationProgramChange::getCurrentVersion, INITIAL_VERSION);
        List<ApplicationProgramChange> existingChanges = appProgramChangeMapper.selectList(queryWrapper);

        // 构建已存在的appId集合，用于快速查找
        Set<String> existingAppIds = existingChanges.stream()
                .map(ApplicationProgramChange::getAppId)
                .collect(Collectors.toSet());

        log.info("变更表中已存在版本为0的记录 {} 条", existingChanges.size());

        LocalDateTime now = LocalDateTime.now();
        int successCount = 0;
        int skipCount = 0;

        // 遍历所有应用程序，检查是否在变更表中存在
        for (ApplicationProgram program : applicationPrograms) {
            String appId = String.valueOf(program.getId());

            if (existingAppIds.contains(appId)) {
                skipCount++;
                log.debug("应用程序 {} 在变更表中已存在，跳过", appId);
                continue;
            }

            try {
                // 使用安全方法处理字符串字段，确保数据类型兼容
                String appCode = safeStringValue(program.getProgramNameEn());

                ApplicationProgramChange change = ApplicationProgramChange.builder()
                        .appId(appId)
                        .appCode(appCode)
                        .systemId(String.valueOf(program.getApplicationId()))
                        .changeType(CHANGE_TYPE_INSERT)
                        .currentVersion(INITIAL_VERSION)
                        .currentData(JsonUtils.toJsonString(program))
                        .operator(SYSTEM_OPERATOR)
                        .changeTime(now)
                        .isProcessed(0)
                        .build();

                appProgramChangeMapper.insert(change);
                successCount++;
                log.debug("成功插入应用程序变更记录，appId: {}, appCode: {}, programCode: {}",
                         appId, appCode, program.getProgramCode());

            } catch (Exception e) {
                log.error("插入应用程序变更记录失败，appId: {}, programCode: {}, programNameEn: {}",
                         appId, program.getProgramCode(), program.getProgramNameEn(), e);
            }
        }

        log.info("应用程序变更表初始化完成，跳过 {} 条已存在记录，成功插入 {} 条新记录",
                skipCount, successCount);
    }
}
